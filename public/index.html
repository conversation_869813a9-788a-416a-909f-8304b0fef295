<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>Precium</title>
	<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,user-scalable=no">
	<meta name="description"
		content="Find out more services to manage your revenue at the best price.  Call us today +91 9665367774 " />
	<meta name="keywords" content=" " />
	<link rel="canonical" href="https://precium.in" />
	<link rel="apple-touch-icon" href="images/logo-purple.svg" />
	<link rel="icon" href="images/logo-purple.ico" />
	<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
		integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.3/dist/umd/popper.min.js"
		integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.1.3/dist/js/bootstrap.min.js"
		integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
		crossorigin="anonymous"></script>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css"
		integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">

	<script src="/js/header.js"></script>
	<script src="/js/footer.js"></script>

	<link rel="stylesheet" type="text/css" href="css/style.css">

</head>

<body>
	<my-header />

	<div class="banner">
		<video autoplay muted loop id="myVideo">
			<source src="./images/Precium Video (1).mp4" type="video/mp4">
		</video>

	</div>

	<!-- <div class="clearfix"></div> -->
	<section class="welcome-section">
		<div class="custome-container">
			<div class="row cardContainer-version">
				<div class="col-md-4 col-sm-6" style="margin-bottom: 10px;">
					<div class="home-page-card  text-center">
						<div>
							<div class="cards-top">
								<h3>AI-Powered Automated Pricing Recommendation</h3>
							</div>
							<p class="section-para">Unlock Your Hotel’s Revenue Potential with AI Powered - Precium -
								“Right Price, Every Time”. With data and market insights, Precium recommends room rates
								to create a balance between occupancy and ADR to generate optimal revenue.</p>
							<br>
						</div>
						<div>
							<center><img class="card-images" src="images/hs_img1.svg"></center>
							<center><a href="/pricing-recommendation"><img src="images/readmore.svg"></a></center>
						</div>
					</div>
				</div>
				<div class="col-md-4 col-sm-6" style="margin-bottom: 10px;">
					<div class="home-page-card text-center">
						<div>
							<div class="cards-top">
								<h3>Forecasting & Budgeting Platform</h3>
							</div>
							<p class="section-para">Budgeting and Forecasting made easy. No more complicated Excel
								sheets and formula errors. Easy to compare data and strategise.</p>
						</div>
						<div>
							<center><img class="card-images" src="images/hs_img2.svg"></center>
							<center><a href="/forecasting-and-budgeting"><img src="images/readmore.svg"></a></center>
						</div>
					</div>
				</div>
				<div class="col-md-4 col-sm-6"
					style="margin-bottom: 10px; display: flex; flex-direction: column; justify-content: space-between;">
					<div class="home-page-card text-center"
						style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between;">
						<div>
							<div class="cards-top">
								<h3>Automated Competitor Rate Shopping & Rate Parity Check Tool</h3>
							</div>
							<p class="section-para">Real-time Competitor Rate shopping tool to analyse competitor
								pricing strategy at a click of a button. </p>
						</div>
						<div>
							<center><img class="card-images" src="images/hs_img3.svg"></center>
							<center><a href="/rate-shopping-and-rate-parity-check-tool"><img
										src="images/readmore.svg"></a></center>
						</div>
					</div>
				</div>
				<div class="clearfix visible-sm hidden-md"></div>
				<div class="col-md-4 col-sm-6" style="margin-bottom: 10px;">
					<div class="home-page-card text-center">
						<div>
							<div class="cards-top">
								<h3>Upcoming City Events Calendar</h3>
							</div>
							<p class="section-para">Turn Local Insights into Smart Revenue Strategy –With our Automated
								City Events Calendar.</p>
						</div>
						<div>
							<center><img class="card-images" src="images/hs_img4.svg"></center>
							<center><a href="/upcoming-city-events-calendar"><img src="images/readmore.svg"></a>
							</center>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<div class="col-md-12 text-center purple-bg">
		<h3>We work with the world's leading organisations to provide<br> collaborative solutions to your business</h3>
	</div>
	<div class="cleaservice-section rfix version-section">
		<section class="gray">
			<div class="custome-container" style="display: flex; justify-content: center;">
				<div class="section_title text-center">
					<h2 style="color: #636363;"> Precium Versions</h2>
					<p class="section-para">Choose Precium version as per your requirement </p>
				</div>
				<div class="cardContainer-version">
					<div class="col-md-4 col-sm-6" style="margin-bottom: 10px;">
						<div class="home-page-card-flex home-page-card text-center">
							<center><img src="images/ver1.svg"></center>
							<h3>Precium 1.0</h3>
							<p class="section-para" style="margin-bottom: 0px;">Simple, Easy to Use, Automated Hotel
								Room Pricing Recommendation Tool.<br />
								Simple upload of Report from your PMS, supported by complex Algorithm and Machine
								learning in the back end, gives us the accurate Price point by day. Pre-defined
								analytical reports make it easier for the Revenue Manager to make the right decision for
								the business. </p>
						</div>
					</div>
					<div class="col-md-4 col-sm-6" style="margin-bottom: 10px;">
						<div class="home-page-card-flex home-page-card text-center">
							<center><img src="images/ver2.svg"></center>
							<h3>Precium 2.0</h3>
							<p class="section-para" style="margin-bottom: 0px;">Right Strategy and Forecasting is very
								important for any business to make Right decisions for your business. Precium 2.0 is
								designed to support General Managers and Revenue Manager to prepare Rooms Budgets by
								Market Segment. Accurate Forecasting by Market Segment by Day is the basis of accurate
								Revenue Management decision making. Precium 2.0 helps Revenue Managers to accurately
								forecast, resulting in Right Yield Management Decision to maximise ADR and RevPAR.
								Precium 2.0 makes Revenue Managers proactive and efficient. Revenue Manager’s pain
								points have been addressed in this version. </p>
						</div>
					</div>
					<div class="clearfix visible-sm hidden-md"></div>
					<div class="col-md-4 col-sm-6" style="margin-bottom: 10px;">
						<div class="home-page-card-flex home-page-card text-center">
							<center><img src="images/ver3.svg"></center>
							<h3>Precium 3.0</h3>
							<p class="section-para">Watch the Space. Launching soon. </p>
						</div>
					</div>
				</div>
				<!--  according new designed -->




			</div>

		</section>
	</div>

	<!-- according to new design new section -->

	<div class="col-md-12 text-center purple-bg">
		<h3>We offer Bespoke Revenue Management Services to<br> fit in with your requirement. </h3>
	</div>
	<section id="Service-Section" class="service-section gray version-section">
		<div class="custome-container">
			<div class="section_title text-center">
				<h2 style="color: #636363;">Services Offered</h2>
			</div>

			<div class="cardContainer cardContainer-version ">


				<!--  according new designed -->


				<div class="col-md-4 col-sm-6 service-card">
					<div class="home-page-card-flex home-page-card text-center">
						<div class="service-card-top">
							<center><img class="service-image" src="images/s1_img.svg"></center>
							<h4>Precium Real-time Competitor Rate Shopping Tool </h4>
						</div>
						<ul class="center-list-items">
							<li value="✓" class="li-custome">One Time Precium setup</li>
							<li value="✓" class="li-custome">Software Support</li>
							<li value="✓" class="li-custome">Precium access for key members of the Team</li>
							<li value="✓" class="li-custome">Software Training</li>
						</ul>
					</div>
				</div>
				<div class="col-md-4 col-sm-6 service-card">
					<div class="home-page-card-flex home-page-card text-center">
						<div class="service-card-top">
							<center><img class="service-image" style="height: 72px;" src="images/precium_software1.svg">
							</center>
							<h4>Precium Software </h4>
						</div>

						<ul class="center-list-items">
							<li value="✓" class="li-custome">Software Training</li>
							<li value="✓" class="li-custome">Software Support</li>
							<li value="✓" class="li-custome">One Time Precium setup</li>
							<li value="✓" class="li-custome">Precium access for key members of the Team</li>
						</ul>
					</div>
				</div>
				<!-- <div class="clearfix visible-sm hidden-md"></div> -->
				<div class="col-md-4 col-sm-6 service-card">
					<div class="home-page-card-flex home-page-card text-center">
						<div class="service-card-top">
							<center><img class="service-image" src="images/s3_img.svg"></center>
							<h4>Precium Software <br> + <br>Revenue Management Consultancy</h4>
						</div>
						<ul class="center-list-items">
							<li value="✓" class="li-custome">One Time Precium setup</li>
							<li value="✓" class="li-custome">Software Training</li>
							<li value="✓" class="li-custome">Software Support</li>
							<li value="✓" class="li-custome">Precium access for key members of the Team</li>
							<li value="✓" class="li-custome">Revenue Management Consultancy Experts Recommendation </li>
							<li value="✓" class="li-custome">Entire Audit of the Revenue Management System </li>
						</ul>
					</div>
				</div>
				<div class="col-md-4 col-sm-6 service-card" style="margin-bottom: 10px;">
					<div class="home-page-card-flex home-page-card text-center">
						<div class="service-card-top">
							<center><img class="service-image" src="images/s4_img.svg"></center>
							<h4>Precium Software <br> + <br> Complete Revenue Management Support Service</h4>
						</div>
						<ul class="center-list-items">
							<li value="✓" class="li-custome">One Time Precium setup</li>
							<li value="✓" class="li-custome">Software Training</li>
							<li value="✓" class="li-custome">Software Support</li>
							<li value="✓" class="li-custome">Precium access for key members of the Team</li>
							<li value="✓" class="li-custome">Dedicated Revenue Manager with Strategy
								Implementation. </li>
							<li value="✓" class="li-custome">Complete Revenue Management Support. </li>
							<li value="✓" class="li-custome">Support with Budgeting & Forecasting. </li>
						</ul>
					</div>
				</div>
				<div class="col-md-4 col-sm-6 service-card">
					<div class="home-page-card-flex home-page-card text-center ">
						<div class="service-card-top">

							<center><img class="service-image" src="images/s5_img.svg"></center>
							<h4 style="font-size: 14px;">Precium Software <br>
								+ <br>Precium Real-time Competitor Rate Shopping Tool <br>+ <br>
								Complete Revenue Management Support Service</h4>
						</div>
						<ul class="center-list-items">
							<li value="✓" class="li-custome">One Time Precium setup</li>
							<li value="✓" class="li-custome">Software Training</li>
							<li value="✓" class="li-custome">Software Support</li>
							<li value="✓" class="li-custome">Precium access for key members of the Team</li>
							<li value="✓" class="li-custome">Dedicated Revenue Manager with Strategy
								Implementation. </li>
							<li value="✓" class="li-custome">Complete Revenue Management Support. </li>
							<li value="✓" class="li-custome">Support with Budgeting & Forecasting. </li>
							<li value="✓" class="li-custome">Real-time Competitor Rate Shopping tool</li>
							<li value="✓" class="li-custome"> Rate Parity Check</li>
							<li value="✓" class="li-custome">Upcoming City Events</li>
						</ul>
					</div>
				</div>



			</div>
		</div>
	</section>



	<div class="col-md-12 text-center purple-bg">
		<h3>One Platform for all your Revenue Management Strategies and Action Planning. <br />
			Access it anytime, anywhere. Data Security Guaranteed. </h3>
	</div>
	<div class="clearfix">
		<section clservice-section ass="version-section">
			<div class="custome-container">
				<div class="expert-parent">
					<div class="expert-content">
						<div class="expert-div ex-div1">
							<div><img src="images/ex_img1.svg" alt=""></div>
							<div>
								<h4>
									Cloud Based Budgeting & Forecast
									</h2>
									<p>Ensure consistent and up-to- date data across all properties</p>
							</div>
						</div>
						<div class="expert-div">
							<div><img src="images/ex_img2.svg" alt=""></div>
							<div>
								<h4>
									Rate Accuracy
								</h4>
								<p>Key stakeholders receive timely, chain Wide insights for informed desicion making</p>
							</div>
						</div>
						<div class="expert-div">
							<div><img src="images/ex_img3.svg" alt=""></div>
							<div>
								<h4>
									Intellegent Pricing Engine
								</h4>
								<p>Leverages historical data, Competitor Pricing, Market trends, Booking windows,
									day-of-week patterns, Demand levels, and more. Predicts optimal daily Room Rates for
									each property using advanced Algorithms. </p>
							</div>
						</div>
					</div>
					<div class="expert-content">
						<div class="expert-div">
							<div><img src="images/ex_img4.svg" alt=""></div>
							<div>
								<h4>
									Centralised Platform
								</h4>
								<p>Manage the entire hotel Portfolio from a single, unified platform.</p>
							</div>
						</div>
						<div class="expert-div">
							<div><img src="images/ex_img5.svg" alt=""></div>
							<div>
								<h4>
									Real-Time insights
								</h4>
								<p>Key stakeholders receive timely, chain Wide insights for informed desicion making</p>
							</div>
						</div>
						<div class="expert-div">
							<div><img src="images/ex_img6.svg" alt=""></div>
							<div>
								<h4>
									Ready-Sales Tools
								</h4>
								<p>Sales team access live data on occupancy, Event Calendars and Competitor Pricing
									while on the move</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Version Carousel Navigation -->
			<div class="version-carousel-nav">
				<button class="version-nav-btn" id="versionPrevBtn">
					<svg viewBox="0 0 24 24" fill="currentColor">
						<path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
					</svg>
					Previous
				</button>
				<button class="version-nav-btn" id="versionNextBtn">
					Next
					<svg viewBox="0 0 24 24" fill="currentColor">
						<path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
					</svg>
				</button>
			</div>
		</section>
	</div>

	<div class="col-md-12 text-center purple-bg">

		<h3>We Support over 500+ Hotels with Rooms<br> Inventory of 50,000+ in 4 Continents </h3>
	</div>
	<div class="clearfix"></div>
	<div class="portfolio">
		<div>
			<div>
				<div class="section_title text-center">
					<div class="managing-section-title">
						<!-- <h2>We now manage over 50 hotels with room inventory of 3500</h2> -->
					</div>
				</div>
				<div class="row cardContainer-version">
					<div class="col-md-4 col-sm-6 no-p">
						<img src="images/portfolio-1.svg" class="img-responsive" style="transform: scale(0.8);" />
					</div>
					<div class="col-md-4 col-sm-6 no-p">
						<img src="images/portfolio-2.svg" class="img-responsive" />
					</div>
					<div class="col-md-4 col-sm-6 no-p">
						<img src="images/portfolio-3.svg" class="img-responsive" />
					</div>
				</div>

				<div class="row cardContainer-version">
					<div>
						<img src="images/index_bottom_img1.png" class="img-responsive" />
					</div>
					<div>
						<img src="images/index_bottom_img2.png" class="img-responsive" />
					</div>

				</div>


			</div>
		</div>
	</div>
	<div class="clearfix"></div>

	<div class="col-md-12 text-center purple-bg " style="margin:60px 0 0 0 ;">
		<h3>Be ahead of the Game. Be Ready Today for Tomorrow. <br />
			Let Technology support your decisions with data. </h3>
	</div>


	<div class="clearfix"></div>

	<my-footer />

	<script src="/js/stickUp.min.js"></script>
	<script type="text/javascript">
		jQuery(function (a) { a(document).ready(function () { a(".navbar-inverse").stickUp() }) });
	</script>
	<script type="text/javascript">
		var canvas = document.getElementById('nokey'),
			can_w = parseInt(canvas.getAttribute('width')),
			can_h = parseInt(canvas.getAttribute('height')),
			ctx = canvas.getContext('2d');

		// console.log(typeof can_w);

		var ball = {
			x: 0,
			y: 0,
			vx: 0,
			vy: 0,
			r: 0,
			alpha: 1,
			phase: 0
		},
			ball_color = {
				r: 255,
				g: 93,
				b: 0
			},
			R = 4,
			balls = [],
			alpha_f = 0.03,
			alpha_phase = 0,

			// Line
			link_line_width = 2,
			dis_limit = 300,
			add_mouse_point = true,
			mouse_in = false,
			mouse_ball = {
				x: 0,
				y: 0,
				vx: 0,
				vy: 0,
				r: 0,
				type: 'mouse'
			};

		// Random speed
		function getRandomSpeed(pos) {
			var min = -1,
				max = 2;
			switch (pos) {
				case 'top':
					return [randomNumFrom(min, max), randomNumFrom(0.2, max)];
					break;
				case 'right':
					return [randomNumFrom(min, -0.2), randomNumFrom(min, max)];
					break;
				case 'bottom':
					return [randomNumFrom(min, max), randomNumFrom(min, -0.2)];
					break;
				case 'left':
					return [randomNumFrom(0.2, max), randomNumFrom(min, max)];
					break;
				default:
					return;
					break;
			}
		}
		function randomArrayItem(arr) {
			return arr[Math.floor(Math.random() * arr.length)];
		}
		function randomNumFrom(min, max) {
			return Math.random() * (max - min) + min;
		}
		console.log(randomNumFrom(0, 200));
		// Random Ball
		function getRandomBall() {
			var pos = randomArrayItem(['top', 'right', 'bottom', 'left']);
			switch (pos) {
				case 'top':
					return {
						x: randomSidePos(can_w),
						y: -R,
						vx: getRandomSpeed('top')[0],
						vy: getRandomSpeed('top')[1],
						r: R,
						alpha: 1,
						phase: randomNumFrom(0, 10)
					}
					break;
				case 'right':
					return {
						x: can_w + R,
						y: randomSidePos(can_h),
						vx: getRandomSpeed('right')[0],
						vy: getRandomSpeed('right')[1],
						r: R,
						alpha: 1,
						phase: randomNumFrom(0, 10)
					}
					break;
				case 'bottom':
					return {
						x: randomSidePos(can_w),
						y: can_h + R,
						vx: getRandomSpeed('bottom')[0],
						vy: getRandomSpeed('bottom')[1],
						r: R,
						alpha: 1,
						phase: randomNumFrom(0, 10)
					}
					break;
				case 'left':
					return {
						x: -R,
						y: randomSidePos(can_h),
						vx: getRandomSpeed('left')[0],
						vy: getRandomSpeed('left')[1],
						r: R,
						alpha: 1,
						phase: randomNumFrom(0, 10)
					}
					break;
			}
		}
		function randomSidePos(length) {
			return Math.ceil(Math.random() * length);
		}

		// Draw Ball
		function renderBalls() {
			Array.prototype.forEach.call(balls, function (b) {
				if (!b.hasOwnProperty('type')) {
					ctx.fillStyle = 'rgba(' + ball_color.r + ',' + ball_color.g + ',' + ball_color.b + ',' + b.alpha + ')';
					ctx.beginPath();
					ctx.arc(b.x, b.y, R, 0, Math.PI * 2, true);
					ctx.closePath();
					ctx.fill();
				}
			});
		}

		// Update balls
		function updateBalls() {
			var new_balls = [];
			Array.prototype.forEach.call(balls, function (b) {
				b.x += b.vx;
				b.y += b.vy;

				if (b.x > -(50) && b.x < (can_w + 50) && b.y > -(50) && b.y < (can_h + 50)) {
					new_balls.push(b);
				}

				// alpha change
				b.phase += alpha_f;
				b.alpha = Math.abs(Math.cos(b.phase));
				// console.log(b.alpha);
			});

			balls = new_balls.slice(0);
		}

		// loop alpha
		function loopAlphaInf() {

		}

		// Draw lines
		function renderLines() {
			var fraction, alpha;
			for (var i = 0; i < balls.length; i++) {
				for (var j = i + 1; j < balls.length; j++) {

					fraction = getDisOf(balls[i], balls[j]) / dis_limit;

					if (fraction < 1) {
						alpha = (1 - fraction).toString();

						ctx.strokeStyle = 'rgba(255,255,255,' + alpha + ')';
						ctx.lineWidth = link_line_width;

						ctx.beginPath();
						ctx.moveTo(balls[i].x, balls[i].y);
						ctx.lineTo(balls[j].x, balls[j].y);
						ctx.stroke();
						ctx.closePath();
					}
				}
			}
		}

		// calculate distance between two points
		function getDisOf(b1, b2) {
			var delta_x = Math.abs(b1.x - b2.x),
				delta_y = Math.abs(b1.y - b2.y);

			return Math.sqrt(delta_x * delta_x + delta_y * delta_y);
		}

		// add balls if there a little balls
		function addBallIfy() {
			if (balls.length < 20) {
				balls.push(getRandomBall());
			}
		}

		// Render
		function render() {
			ctx.clearRect(0, 0, can_w, can_h);

			renderBalls();

			renderLines();

			updateBalls();

			addBallIfy();

			window.requestAnimationFrame(render);
		}

		// Init Balls
		function initBalls(num) {
			for (var i = 1; i <= num; i++) {
				balls.push({
					x: randomSidePos(can_w),
					y: randomSidePos(can_h),
					vx: getRandomSpeed('top')[0],
					vy: getRandomSpeed('top')[1],
					r: R,
					alpha: 1,
					phase: randomNumFrom(0, 10)
				});
			}
		}
		// Init Canvas
		function initCanvas() {
			canvas.setAttribute('width', window.innerWidth);
			canvas.setAttribute('height', window.innerHeight);

			can_w = parseInt(canvas.getAttribute('width'));
			can_h = parseInt(canvas.getAttribute('height'));
		}
		window.addEventListener('resize', function (e) {
			console.log('Window Resize...');
			initCanvas();
		});

		function goMovie() {
			initCanvas();
			initBalls(30);
			window.requestAnimationFrame(render);
		}
		goMovie();

		// Mouse effect
		canvas.addEventListener('mouseenter', function () {
			console.log('mouseenter');
			mouse_in = true;
			balls.push(mouse_ball);
		});
		canvas.addEventListener('mouseleave', function () {
			console.log('mouseleave');
			mouse_in = false;
			var new_balls = [];
			Array.prototype.forEach.call(balls, function (b) {
				if (!b.hasOwnProperty('type')) {
					new_balls.push(b);
				}
			});
			balls = new_balls.slice(0);
		});
		canvas.addEventListener('mousemove', function (e) {
			var e = e || window.event;
			mouse_ball.x = e.pageX;
			mouse_ball.y = e.pageY;
			// console.log(mouse_ball);
		});

	</script>

	<!-- Version Carousel JavaScript -->
	<script>
		document.addEventListener('DOMContentLoaded', function () {
			// Only initialize carousel on screens less than 1200px
			function initVersionCarousel() {
				if (window.innerWidth >= 1200) return;

				const carousel = document.querySelector('.version-section .cardContainer-version');
				const cards = carousel.querySelectorAll('.col-md-4, .col-sm-6');
				const prevBtn = document.getElementById('versionPrevBtn');
				const nextBtn = document.getElementById('versionNextBtn');

				if (!carousel || !cards.length || !prevBtn || !nextBtn) return;

				let currentIndex = 0;
				const totalCards = cards.length;

				function updateCarousel() {
					// Move cards vertically
					const translateY = -currentIndex * 100; // 100% per card
					cards.forEach((card, index) => {
						card.style.transform = `translateY(${translateY}%)`;
					});

					// Update button states
					prevBtn.disabled = currentIndex === 0;
					nextBtn.disabled = currentIndex === totalCards - 1;
				}

				function goToPrevious() {
					if (currentIndex > 0) {
						currentIndex--;
						updateCarousel();
					}
				}

				function goToNext() {
					if (currentIndex < totalCards - 1) {
						currentIndex++;
						updateCarousel();
					}
				}

				// Event listeners
				prevBtn.addEventListener('click', goToPrevious);
				nextBtn.addEventListener('click', goToNext);

				// Initialize
				updateCarousel();

				// Handle window resize
				window.addEventListener('resize', function () {
					if (window.innerWidth >= 1200) {
						// Reset transforms on larger screens
						cards.forEach(card => {
							card.style.transform = '';
						});
					} else {
						// Reinitialize carousel
						updateCarousel();
					}
				});
			}

			// Initialize carousel
			initVersionCarousel();
		});
	</script>
</body>

<!-- Mirrored from www.precium.com/ by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 08 Mar 2022 10:56:01 GMT -->

</html>