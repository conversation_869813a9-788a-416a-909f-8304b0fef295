/* Blogs Page Styles */
.blogs-container {
  padding: 0vw 8vw 6vw 8vw;
  margin: 0 auto;
  font-family: "Poppins", sans-serif;
}

.blogs-header {
  display: flex;
  padding: 15px;
  justify-content: space-between;
  font-family: "Poppins";
  font-size: 16px;
  font-weight: 500;
}

.blogs-icon img {
  max-width: 300px;
  border-radius: 12px;
}

.blogs-title {
  font-size: 64px;
  padding-top: 95px;
  font-weight: 500;
  color: #004aad;
  margin: 0;
}

.blogs-grid {
  display: flex;
  flex-direction: column;
  /* grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)); */
  gap: 30px;
  margin-top: 20px;
}

.blog-card {
  background: white;
  border-radius: 16px;
  padding: 30px 50px 30px 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.blog-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.blog-content {
  display: flex;
  gap: 25px;
  align-items: flex-start;
}

.blog-text {
  flex: 1;
}

.blog-title {
  font-size: 44px;
  font-weight: 500;
  color: #000000;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.blog-description {
  font-size: 20px;
  color: #757575;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.read-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #004aad;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.read-more-btn:hover {
  transform: translateX(2px);
  text-decoration: none;
  color: white;
}

.read-more-btn::after {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.read-more-btn:hover::after {
  transform: translateX(3px);
}

.blog-image {
  width: 280px;
  border-radius: 12px;
  object-fit: cover;
  flex-shrink: 0;
}

/* Responsive Design */

@media (max-width: 1024px) {
  .blogs-container {
    padding: 0vw 8vw 6vw 6vw;
  }
}
@media (max-width: 768px) {
  .blogs-container {
    padding: 20px 15px;
  }

  .blogs-header {
    flex-direction: column;
    text-align: center;
  }

  .blogs-title {
    padding-top: 0;
  }

  .blogs-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .blog-card {
    padding: 20px;
  }

  .blog-content {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .blog-image {
    width: 400px;
    height: 200px;
    order: -1;
  }

  .blog-title {
    font-size: 20px;
  }

  .blog-description {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .blogs-title {
    font-size: 36px;
  }
  .blog-title {
    font-size: 18px;
  }

  .blog-description {
    font-size: 14px;
  }
}
