/* Our Clients Page Styles */

/* Hero Section */
.clients-hero {
  background: linear-gradient(135deg, #327bd4 0%, #4a7bc8 100%);
  color: white;
  padding: 30px 0 60px;
  position: relative;
  overflow: hidden;
}

.clients-hero::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: url('/images/our-clients.svg') no-repeat center right;
  background-size: contain;
  opacity: 0.1;
  z-index: 1;
}

.clients-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.clients-hero-text h1 {
  font-size: 64px;
  font-weight: 500;
  margin-bottom: 30px;
  color: white;
  font-family: "Poppins", sans-serif;
}

.clients-hero-text p {
  font-size: 18px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
  max-width: 500px;
}

.clients-hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.clients-hero-image img {
  max-width: 100%;
  height: auto;
  /* filter: brightness(0) invert(1); */
}

/* Main Content Section */
.clients-main {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.clients-stats {
  text-align: center;
  margin-bottom: 80px;
}

.clients-stats h2 {
  font-size: 42px;
  font-weight: 600;
  color: #333;
  font-family: "Poppins", sans-serif;
  margin-bottom: 0;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.2;
  text-align: center;
}

/* Client Logos Grid */
.clients-grid-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  justify-content: center;
  position: relative;
  display: flex;
  align-items: center;
  gap: 40px;
}

.clients-grid-wrapper {
  overflow: hidden;
  width: 80dvw;
  flex-shrink: 0;
}

.clients-grid {
  display: grid;
  grid-auto-columns: 6vw;
    grid-auto-flow: column;
  /* grid-template-columns: repeat(8, 1fr); 8 columns to match original design */
  grid-template-rows: repeat(3, 1fr); /* 3 rows as in original */
  gap: 15px;
  transition: transform 0.3s ease;
  column-gap: 7rem;
}

.client-logo {
  background: white;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  aspect-ratio: 1;
  height: 80px;
  border: 1px solid #e8e8e8;
}

.client-logo:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
  border-color: #306fbc;
}

.client-logo img {
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
  transition: all 0.3s ease;
}

.client-logo:hover img {
  transform: scale(1.05);
}

/* Navigation Arrows */
.nav-arrow {
  background: #306fbc;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(48, 111, 188, 0.3);
  position: relative;
  z-index: 10;
}

.nav-arrow:hover {
  background: #2558a3;
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(48, 111, 188, 0.4);
}

.nav-arrow:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.nav-arrow svg {
  width: 28px;
  height: 28px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .clients-hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .clients-hero-text h1 {
    font-size: 48px;
  }
  

  
  .clients-stats h2 {
    font-size: 32px;
  }
}

@media (max-width: 768px) {
  .clients-hero {
    padding: 60px 0 40px;
  }
  
  .clients-hero-text h1 {
    font-size: 36px;
  }
  
  .clients-hero-text p {
    font-size: 16px;
  }
  
  .clients-main {
    padding: 60px 0;
  }
  
  .clients-stats {
    margin-bottom: 60px;
  }
  
  .clients-stats h2 {
    font-size: 24px;
    line-height: 1.3;
  }
  
  .clients-grid-container {
    gap: 15px;
    padding: 0 15px;
  }
  
  .client-logo {
    padding: 15px;
    min-height: 72px;
  }
  
  .nav-arrow {
    width: 40px;
    height: 40px;
  }
  
  .nav-arrow svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .clients-hero-content {
    padding: 0 15px;
  }
  
  .clients-hero-text h1 {
    font-size: 28px;
  }
  
  .clients-stats h2 {
    font-size: 20px;
    padding: 0 15px;
  }
  
  .client-logo {
    min-height: 80px;
    padding: 10px;
  }
}

/* Animation for smooth transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.client-logo {
  animation: fadeInUp 0.6s ease forwards;
}

.client-logo:nth-child(1) { animation-delay: 0.1s; }
.client-logo:nth-child(2) { animation-delay: 0.2s; }
.client-logo:nth-child(3) { animation-delay: 0.3s; }
.client-logo:nth-child(4) { animation-delay: 0.4s; }
.client-logo:nth-child(5) { animation-delay: 0.5s; }
.client-logo:nth-child(6) { animation-delay: 0.6s; }
.client-logo:nth-child(7) { animation-delay: 0.7s; }
.client-logo:nth-child(8) { animation-delay: 0.8s; }
