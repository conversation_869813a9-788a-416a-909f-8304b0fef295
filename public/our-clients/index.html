<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>Our Clients - Precium</title>
	<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,user-scalable=no">
	<meta name="description"
		content="Discover our extensive client portfolio. We serve 500+ hotels with 50,000+ inventory across 4+ continents, providing AI-powered revenue management solutions." />
	<meta name="keywords" content="hotel clients, revenue management, hotel portfolio, precium clients" />
	<link rel="canonical" href="https://precium.in/our-clients" />
	<link rel="apple-touch-icon" href="/images/logo-purple.svg" />
	<link rel="icon" href="/images/logo-purple.ico" />
	<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
		integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.3/dist/umd/popper.min.js"
		integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.1.3/dist/js/bootstrap.min.js"
		integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
		crossorigin="anonymous"></script>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css"
		integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">

	<link rel="stylesheet" type="text/css" href="/css/style.css">
	<link rel="stylesheet" type="text/css" href="/our-clients/style.css">

	<script src="/js/header.js"></script>
	<script src="/js/footer.js"></script>
</head>

<body>
	<my-header></my-header>

	<!-- Hero Section -->
	<div class="clients-hero">
		<div class="clients-hero-content">
			<div class="clients-hero-text">
				<h1>Our Clients</h1>
				<p>An organisation knowledge is one of the valuable asset. At Precium, We provide business insights to
					revenue managers to make right decision to support revenue growth</p>
			</div>
			<div class="clients-hero-image">
				<img src="/images/our-clients.svg" alt="Our Clients">
			</div>
		</div>
	</div>

	<!-- Main Content Section -->
	<section class="clients-main">
		<div class="clients-stats">
			<h2>Serving 500+ Hotels with 50000 + inventory in 4+ Continents</h2>
		</div>

		<!-- Client Logos Grid -->
		<div class="clients-grid-container">
			<button class="nav-arrow nav-arrow-left" id="prevBtn">
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round" />
				</svg>
			</button>

			<div class="clients-grid-wrapper">
				<div class="clients-grid" id="clientsGrid">
					<!-- Row 1 -->
					<div class="client-logo">
						<img src="/images/icon-1.png" alt="Suryaa Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-2.png" alt="Red Fox Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-3.png" alt="Hard Rock Cafe" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-4.png" alt="IRA Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-5.png" alt="Tribe Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-6.png" alt="Lemon Tree" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-7.png" alt="Adagio Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-8.png" alt="Rosana Hotel" />
					</div>

					<!-- Row 2 -->
					<div class="client-logo">
						<img src="/images/icon-9.png" alt="Almara Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-10.png" alt="Orchid Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/icon-11.png" alt="Takara Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/portfolio-1.svg" alt="VITS Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/portfolio-2.svg" alt="Radisson Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/portfolio-3.svg" alt="Courtyard Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/portfolio-4.svg" alt="Golden Tulip" />
					</div>
					<div class="client-logo">
						<img src="/images/portfolio-5.svg" alt="Q Hotel" />
					</div>

					<!-- Row 3 -->
					<div class="client-logo">
						<img src="/images/portfolio-6.svg" alt="Hotel Partner" />
					</div>
					<div class="client-logo">
						<img src="/images/hs_img1.svg" alt="The Grand Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/hs_img2.svg" alt="Luxury Resort" />
					</div>
					<div class="client-logo">
						<img src="/images/hs_img3.svg" alt="Gulf Inn Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/hs_img4.svg" alt="Swissotel" />
					</div>
					<div class="client-logo">
						<img src="/images/ex_img1.svg" alt="Kerala Hotel" />
					</div>
					<div class="client-logo">
						<img src="/images/ex_img2.svg" alt="Resort Partner" />
					</div>
					<div class="client-logo">
						<img src="/images/ex_img3.svg" alt="The Luxury" />
					</div>
				</div>
			</div>

			<button class="nav-arrow nav-arrow-right" id="nextBtn">
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round" />
				</svg>
			</button>
		</div>
	</section>

	<my-footer></my-footer>

	<script src="/js/stickUp.min.js"></script>
	<script type="text/javascript">
		jQuery(function (a) { a(document).ready(function () { a(".navbar-inverse").stickUp() }) });
	</script>

	<script>
		// Client logos carousel functionality
		document.addEventListener('DOMContentLoaded', function () {
			const grid = document.getElementById('clientsGrid');
			const prevBtn = document.getElementById('prevBtn');
			const nextBtn = document.getElementById('nextBtn');
			let currentIndex = 0;
			const totalItems = grid.children.length;
			console.log({ totalItems });
			// Responsive items per view
			function getItemsPerView() {
				if (window.innerWidth <= 480) return 4;
				if (window.innerWidth <= 768) return 5;
				if (window.innerWidth <= 900) return 6;
				return 8; // 8 logos visible at once as in original design
			}

			let itemsPerView = getItemsPerView();
			let maxIndex = Math.max(0, totalItems / itemsPerView);

			function updateGrid() {
				// Move by one column at a time based on current viewport
				const columnWidth = 100 / itemsPerView;
				const translateX = -(currentIndex * columnWidth);
				grid.style.transform = `translateX(${translateX}%)`;

				// Update button states
				prevBtn.disabled = currentIndex === 0;
				nextBtn.disabled = currentIndex +1 >= maxIndex;
			}

			prevBtn.addEventListener('click', function () {
				if (currentIndex > 0) {
					currentIndex -= 1; // Move one column at a time
					updateGrid();
				}
			});

			nextBtn.addEventListener('click', function () {
				if (currentIndex < maxIndex) {
					currentIndex += 1; // Move one column at a time
					updateGrid();
				}
			});

			// Handle window resize
			window.addEventListener('resize', function () {
				itemsPerView = getItemsPerView();
				maxIndex = Math.max(0, totalItems - itemsPerView);
				if (currentIndex > maxIndex) {
					currentIndex = maxIndex;
				}
				currentIndex=0;
				updateGrid();
			});

			// Initialize
			updateGrid();
		});
	</script>
</body>

</html>